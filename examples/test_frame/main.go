package main

import (
	"fmt"
	"strings"

	"github.com/oxio/aia/internal/ui"
)

func main() {
	// Create a very long content that will exceed terminal height
	var contentLines []string
	for i := 1; i <= 100; i++ {
		contentLines = append(contentLines, fmt.Sprintf("Line %d of test content", i))
	}
	longContent := strings.Join(contentLines, "\n")

	// Display the content in a frame
	ui.ShowInfoMessage(longContent)
}
