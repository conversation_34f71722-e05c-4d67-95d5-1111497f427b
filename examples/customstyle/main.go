package main

import (
	"fmt"
	"strings"

	"github.com/charmbracelet/lipgloss"
	"github.com/oxio/aia/internal/teaframe"
)

func main() {
	// Create a new CustomStyle
	style := teaframe.NewCustomStyle().
		WithProcessContent(true).
		WithTransform(strings.ToUpper)

	// Apply lipgloss styling
	style = style.ApplyStyle(lipgloss.NewStyle().
		BorderStyle(lipgloss.RoundedBorder()).
		BorderForeground(lipgloss.Color("63")). // Purple border
		Padding(1, 2).
		Width(50))

	// Basic rendering
	fmt.Println("Basic rendering:")
	fmt.Println(style.Render("Hello, World!"))
	fmt.Println()

	// Rendering with a title
	fmt.Println("Rendering with a title:")
	fmt.Println(style.RenderWithTitle("TITLE", "This is content with a title in the border"))
	fmt.Println()

	// Rendering with a footer
	fmt.Println("Rendering with a footer:")
	fmt.Println(style.RenderWithFooter("This is content with a footer in the border", "FOOTER"))
	fmt.Println()

	// Rendering with both title and footer
	fmt.Println("Rendering with both title and footer:")
	fmt.Println(style.RenderWithTitleAndFooter("HEADER", "This is content with both a title and footer", "FOOTER"))
	fmt.Println()

	// Rendering with automatic wrapping
	longText := "This is a very long text that should be automatically wrapped based on the width of the style. " +
		"The wrapping should respect the width, padding, and border settings of the style."
	fmt.Println("Rendering with automatic wrapping:")
	fmt.Println(style.RenderWrapped(longText))
}
