package main

import (
	_ "embed"
	"github.com/oxio/aia/cmd"
	"github.com/oxio/aia/internal/config"
	log "github.com/sirupsen/logrus"
	"gopkg.in/natefinch/lumberjack.v2"
)

//go:embed VERSION
var appVersion string

func main() {
	log.SetOutput(&lumberjack.Logger{
		Filename:   config.GetMainLogFilePath(),
		MaxSize:    10,
		MaxBackups: 3,
		MaxAge:     356,
		Compress:   true,
	})
	log.SetFormatter(&log.TextFormatter{})
	log.SetLevel(log.DebugLevel)

	defer func() {
		log.Debug("quitting AIA")
	}()

	log.Debug("starting AIA")
	cmd.Execute(appVersion)
}
