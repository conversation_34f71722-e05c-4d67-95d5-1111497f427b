package paging

import (
	"strings"
)

// ChunkedLineWriter processes terminal output into chunks based on visible line count.
type ChunkedLineWriter struct {
	linesPerChunk int
	lineBuffer    []string
	chunks        []string
	partialLine   string
}

// NewChunkedLineWriter constructs a writer with chunking by lines.
func NewChunkedLineWriter(linesPerChunk int) *ChunkedLineWriter {
	if linesPerChunk <= 0 {
		panic("linesPerChunk must be greater than 0")
	}
	return &ChunkedLineWriter{
		linesPerChunk: linesPerChunk,
		chunks:        make([]string, 0),
	}
}

// Write implements io.Writer and processes input into visible line chunks.
func (clw *ChunkedLineWriter) Write(p []byte) (n int, err error) {
	input := clw.partialLine + string(p)
	lines := splitPreserveNewline(input)
	if len(lines) == 0 {
		return len(p), nil
	}

	clw.partialLine = ""

	// If the input doesn't end with a newline, buffer the last partial line
	if !strings.HasSuffix(input, "\n") {
		clw.partialLine = lines[len(lines)-1]
		lines = lines[:len(lines)-1]
	}

	for _, line := range lines {
		clw.lineBuffer = append(clw.lineBuffer, line)
		if clw.visibleLineCount() >= clw.linesPerChunk {
			clw.flushChunk()
		}
	}

	return len(p), nil
}

// Close flushes any remaining buffered content as a chunk.
func (clw *ChunkedLineWriter) Close() error {
	if clw.partialLine != "" {
		clw.lineBuffer = append(clw.lineBuffer, clw.partialLine)
		clw.partialLine = ""
	}
	clw.flushChunk()
	return nil
}

// visibleLineCount counts actual displayed lines (not control sequences)
func (clw *ChunkedLineWriter) visibleLineCount() int {
	count := 0
	for _, line := range clw.lineBuffer {
		if strings.Contains(line, "\n") {
			count++
		}
	}
	return count
}

// flushChunk stores a chunk of buffered lines in the chunks slice.
func (clw *ChunkedLineWriter) flushChunk() {
	if len(clw.lineBuffer) == 0 {
		return
	}
	chunk := strings.Join(clw.lineBuffer, "")
	clw.chunks = append(clw.chunks, chunk)
	clw.lineBuffer = clw.lineBuffer[:0]
}

// Chunks returns all the chunks that have been processed.
func (clw *ChunkedLineWriter) Chunks() []string {
	return clw.chunks
}

// splitPreserveNewline splits input into lines keeping the newline char.
func splitPreserveNewline(s string) []string {
	var lines []string
	var buf strings.Builder

	for i := 0; i < len(s); i++ {
		ch := s[i]
		buf.WriteByte(ch)

		if ch == '\n' {
			lines = append(lines, buf.String())
			buf.Reset()
		}
	}
	if buf.Len() > 0 {
		lines = append(lines, buf.String())
	}
	return lines
}
