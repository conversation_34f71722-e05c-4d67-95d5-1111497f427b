package paging

import (
	"strings"
	"testing"
)

func TestChunkedLineWriter_BasicFunctionality(t *testing.T) {
	writer := NewChunkedLineWriter(3)

	// Write some lines
	content := "Line 1\nLine 2\nLine 3\nLine 4\nLine 5\n"
	_, err := writer.Write([]byte(content))
	if err != nil {
		t.Fatalf("Write failed: %v", err)
	}

	err = writer.Close()
	if err != nil {
		t.Fatalf("Close failed: %v", err)
	}

	chunks := writer.Chunks()
	
	// Should have 2 chunks: first with 3 lines, second with 2 lines
	if len(chunks) != 2 {
		t.<PERSON><PERSON><PERSON>("Expected 2 chunks, got %d", len(chunks))
	}

	// First chunk should have exactly 3 lines
	firstChunkLines := strings.Count(chunks[0], "\n")
	if firstChunkLines != 3 {
		t.Errorf("Expected 3 lines in first chunk, got %d", firstChunkLines)
	}

	// Second chunk should have 2 lines
	secondChunkLines := strings.Count(chunks[1], "\n")
	if secondChunkLines != 2 {
		t.<PERSON><PERSON><PERSON>("Expected 2 lines in second chunk, got %d", secondChunkLines)
	}

	// Verify content
	expectedFirstChunk := "Line 1\nLine 2\nLine 3\n"
	expectedSecondChunk := "Line 4\nLine 5\n"
	
	if chunks[0] != expectedFirstChunk {
		t.Errorf("First chunk content mismatch.\nExpected: %q\nGot: %q", expectedFirstChunk, chunks[0])
	}
	
	if chunks[1] != expectedSecondChunk {
		t.Errorf("Second chunk content mismatch.\nExpected: %q\nGot: %q", expectedSecondChunk, chunks[1])
	}
}

func TestChunkedLineWriter_PartialLines(t *testing.T) {
	writer := NewChunkedLineWriter(2)

	// Write partial content without newline
	_, err := writer.Write([]byte("Partial"))
	if err != nil {
		t.Fatalf("Write failed: %v", err)
	}

	// Complete the line
	_, err = writer.Write([]byte(" line\nComplete line\n"))
	if err != nil {
		t.Fatalf("Write failed: %v", err)
	}

	err = writer.Close()
	if err != nil {
		t.Fatalf("Close failed: %v", err)
	}

	chunks := writer.Chunks()
	
	// Should have 1 chunk with 2 lines
	if len(chunks) != 1 {
		t.Errorf("Expected 1 chunk, got %d", len(chunks))
	}

	expected := "Partial line\nComplete line\n"
	if chunks[0] != expected {
		t.Errorf("Chunk content mismatch.\nExpected: %q\nGot: %q", expected, chunks[0])
	}
}

func TestChunkedLineWriter_IncrementalWrites(t *testing.T) {
	writer := NewChunkedLineWriter(2)

	// Write lines one by one
	lines := []string{"First\n", "Second\n", "Third\n", "Fourth\n"}
	
	for _, line := range lines {
		_, err := writer.Write([]byte(line))
		if err != nil {
			t.Fatalf("Write failed: %v", err)
		}
	}

	err := writer.Close()
	if err != nil {
		t.Fatalf("Close failed: %v", err)
	}

	chunks := writer.Chunks()
	
	// Should have 2 chunks: first with 2 lines, second with 2 lines
	if len(chunks) != 2 {
		t.Errorf("Expected 2 chunks, got %d", len(chunks))
	}

	expectedFirstChunk := "First\nSecond\n"
	expectedSecondChunk := "Third\nFourth\n"
	
	if chunks[0] != expectedFirstChunk {
		t.Errorf("First chunk content mismatch.\nExpected: %q\nGot: %q", expectedFirstChunk, chunks[0])
	}
	
	if chunks[1] != expectedSecondChunk {
		t.Errorf("Second chunk content mismatch.\nExpected: %q\nGot: %q", expectedSecondChunk, chunks[1])
	}
}

func TestChunkedLineWriter_Reset(t *testing.T) {
	writer := NewChunkedLineWriter(2)

	// Write some content
	_, err := writer.Write([]byte("Line 1\nLine 2\n"))
	if err != nil {
		t.Fatalf("Write failed: %v", err)
	}

	// Reset and verify
	writer.Reset()
	
	chunks := writer.Chunks()
	if len(chunks) != 0 {
		t.Errorf("Expected 0 chunks after reset, got %d", len(chunks))
	}

	// Write new content after reset
	_, err = writer.Write([]byte("New line\n"))
	if err != nil {
		t.Fatalf("Write after reset failed: %v", err)
	}

	err = writer.Close()
	if err != nil {
		t.Fatalf("Close failed: %v", err)
	}

	chunks = writer.Chunks()
	if len(chunks) != 1 {
		t.Errorf("Expected 1 chunk after reset and new write, got %d", len(chunks))
	}

	expected := "New line\n"
	if chunks[0] != expected {
		t.Errorf("Chunk content mismatch after reset.\nExpected: %q\nGot: %q", expected, chunks[0])
	}
}

func TestChunkedLineWriter_EmptyInput(t *testing.T) {
	writer := NewChunkedLineWriter(3)

	// Write empty content
	_, err := writer.Write([]byte(""))
	if err != nil {
		t.Fatalf("Write failed: %v", err)
	}

	err = writer.Close()
	if err != nil {
		t.Fatalf("Close failed: %v", err)
	}

	chunks := writer.Chunks()
	if len(chunks) != 0 {
		t.Errorf("Expected 0 chunks for empty input, got %d", len(chunks))
	}
}
