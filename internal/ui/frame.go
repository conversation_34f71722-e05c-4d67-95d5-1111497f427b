package ui

import (
	"bufio"
	"github.com/charmbracelet/bubbles/spinner"
	tea "github.com/charmbracelet/bubbletea"
	"github.com/charmbracelet/glamour"
	"github.com/charmbracelet/lipgloss"
	"github.com/oxio/aia/internal/paging"
	"github.com/oxio/aia/internal/terminal"
	"io"
	"strings"
	"sync"
)

type frameModelPaging struct {
	Overflown       bool
	OverflowContent string
}

type framePaging struct {
	currentPageIdx int
}

// FrameModel represents a styled frame component
type FrameModel struct {
	Content          string
	plainContent     string
	lastValidContent string
	Width            int
	Height           int
	config           FrameConfig
	QuitKeys         []string
	AutoQuit         bool
	quitting         bool
	spinnerManager   *SpinnerManagerModel
	LoadingSpinner   *SpinnerWrapper
	ThinkingSpinner  *SpinnerWrapper
	isThinking       bool
	isConnecting     bool
	paging           *frameModelPaging
	pag              *framePaging
	glamourRenderer  *glamour.TermRenderer
	writer           *paging.ChunkedLineWriter
}

type contentMsg string

// NewFrame creates a new frame with default settings
func NewFrame(content string, writer *paging.ChunkedLineWriter, config FrameConfig) FrameModel {
	// Get the terminal dimensions
	termWidth := terminal.GetWidth()
	termHeight := terminal.GetHeight()

	// Calculate default width as 80% of terminal width
	defaultWidth := int(float64(termWidth) * 0.8)

	// Ensure the width is reasonable (between 40 and 120 characters)
	if defaultWidth < 40 {
		defaultWidth = 40
	} else if defaultWidth > 120 {
		defaultWidth = 120
	}

	r, _ := glamour.NewTermRenderer(
		glamour.WithAutoStyle(),
		glamour.WithPreservedNewLines(),
		glamour.WithEmoji(),
		glamour.WithWordWrap(80), // FIXME: set actual term width
	)

	return FrameModel{
		Content:         content,
		Width:           defaultWidth,
		Height:          termHeight,
		config:          config,
		QuitKeys:        []string{"q", "ctrl+c", "esc"},
		AutoQuit:        false, // Auto quit by default
		spinnerManager:  NewSpinnerManagerModel(),
		LoadingSpinner:  NewLoadingSpinnerWrapper(config).SetTitle("Connecting"),
		ThinkingSpinner: NewThinkingSpinnerWrapper(config),
		isConnecting:    true,
		paging:          &frameModelPaging{},
		pag:             &framePaging{},
		glamourRenderer: r,
		writer:          writer,
	}
}

func (m FrameModel) WithWidth(width int) FrameModel {
	m.Width = width
	return m
}

// WithQuitKeys sets the keys that will quit the program
func (m FrameModel) WithQuitKeys(keys []string) FrameModel {
	m.QuitKeys = keys
	return m
}

// WithAutoQuit sets whether the frame should automatically quit
func (m FrameModel) WithAutoQuit(autoQuit bool) FrameModel {
	m.AutoQuit = autoQuit
	return m
}

func (m FrameModel) WithLoadingSpinner(spinner *SpinnerWrapper) FrameModel {
	m.LoadingSpinner = spinner
	return m
}

func (m FrameModel) WithThinkingSpinner(spinner *SpinnerWrapper) FrameModel {
	m.ThinkingSpinner = spinner
	return m
}

// Init initializes the component
func (m FrameModel) Init() tea.Cmd {
	// If AutoQuit is true, quit immediately after rendering
	if m.AutoQuit {
		return tea.Quit
	}

	mgr, cmd := m.spinnerManager.Update(m.LoadingSpinner)
	m.spinnerManager = mgr.(*SpinnerManagerModel)
	return cmd
}

// Update handles messages and updates the model
func (m FrameModel) Update(msg tea.Msg) (tea.Model, tea.Cmd) {
	var err error
	if m.paging.Overflown {
		return m, tea.Quit
	}

	var cmds []tea.Cmd

	switch msg := msg.(type) {

	case tea.KeyMsg:
		// Check if the pressed key is in the quit keys list
		for _, key := range m.QuitKeys {
			if msg.String() == key {
				m.quitting = true
				return m, tea.Quit
			}
		}

	case tea.WindowSizeMsg:
		// TODO: handle this properly. Can it even be done? middle rendering it should be const because of pagination.
		m.Width = msg.Width
		m.Height = msg.Height

	case contentMsg:
		m.plainContent += string(msg)

		m.isConnecting = false
		m.isThinking = false

		// Make sure we have enough content to check if the <think> tag has been opened
		if len(m.plainContent) < 6 {
			m.Content = ""
			return m, nil
		}

		// Check if we are inside an open <think> tag
		thinkStartIdx := strings.LastIndex(m.plainContent, "<think>")
		thinkEndIdx := strings.LastIndex(m.plainContent, "</think>")
		// Check if <think> exists and either </think> doesn't exist or is before <think>
		if thinkStartIdx != -1 && (thinkEndIdx == -1 || thinkEndIdx < thinkStartIdx) {
			m.isThinking = true
		}

		if m.isThinking {
			// We are inside the <think> block, show the thinking spinner and hide content
			m.Content = "" // Clear content to show spinner later

			// Switch to the thinking spinner if it's not already the active one
			// Note: This might generate redundant spinner commands if called repeatedly,
			// but the spinner manager should handle it gracefully.
			mgr, cmd := m.spinnerManager.Update(m.ThinkingSpinner)
			m.spinnerManager = mgr.(*SpinnerManagerModel)
			if cmd != nil {
				cmds = append(cmds, cmd, m.spinnerManager.GetTick())
			}
			// Return early as we don't want to render the <think> content
			return m, tea.Batch(cmds...)
		}
		// If we were thinking but now </think> is received, the spinner
		// should ideally switch back. However, the default flow will
		// now render content, hiding the spinner automatically.
		// If content becomes empty *after* thinking, the loading spinner
		// might reappear based on the View() logic.

		// If not thinking, extract the actual content to be rendered
		// This assumes <think>...</think> blocks should not be rendered.
		if thinkStartIdx != -1 && thinkEndIdx != -1 && thinkEndIdx > thinkStartIdx {
			// Remove the last completed <think>...</think> block for rendering
			// This is a simple approach; might need more robust parsing for complex cases.
			m.plainContent = m.plainContent[:thinkStartIdx] + m.plainContent[thinkEndIdx+len("</think>"):]
			// Reset lastValidContent as well if needed, although glamour handles errors.
			// Let glamour try to render the modified content.
		}

		//log.Debug("plainContent: ", m.plainContent)

		stringWriter := strings.Builder{}

		m.writer.Reset()
		if m.config.IsRichText() {
			err = m.glamourRenderer.RenderBytesTo([]byte(m.plainContent), m.writer) // TODO: handle error using lastValidContent\
		} else {
			_, err = m.writer.Write([]byte(m.plainContent))
			_, err = stringWriter.Write([]byte(m.plainContent))
		}

		if err != nil {
			panic(err)
		}

		//_ = m.writer.Close()

		//m.Content = stringWriter.String()

		if len(m.writer.Chunks()) > 0 {
			//log.Debug("Chunks: ", m.writer.Chunks()[0])
			m.Content = m.writer.Chunks()[m.pag.currentPageIdx]
		}

		//currentContent := m.plainContent
		//plainLines := strings.Split(currentContent, "\n")
		//renderedLines := plainLines
		//
		//if m.config.IsRichText() {
		//	currentRichContent, err := m.glamourRenderer.Render(m.plainContent)
		//	if err == nil {
		//		currentContent = currentRichContent
		//		renderedLines = strings.Split(currentContent, "\n")
		//	} else {
		//		currentContent = m.lastValidContent
		//	}
		//}
		//
		////for renderedLines[len(renderedLines)-1] == "" {
		////	renderedLines = renderedLines[:len(renderedLines)-1]
		////}
		//
		////log.Debug("--- New rendering ---")
		//////log.Debug("Current content ", currentContent)
		////for i := 0; i < len(renderedLines); i++ {
		////	log.Debug("Line ", i, " [len = ", terminal.StringWidth(renderedLines[i]), "]: ", string(renderedLines[i]))
		////}
		//
		//frameHeightOverhead := m.config.MarginY()*2 + m.config.PaddingY()*2 + 2
		//if len(renderedLines) > (terminal.GetHeight()-frameHeightOverhead) && m.paging != nil {
		//	//log.Debug("### SPLITTING ###")
		//	//log.Debug("Lines: ", len(renderedLines))
		//	//log.Debug("Overflow: ", m.paging.OverflowContent)
		//
		//	m.paging.Overflown = true
		//
		//	if m.config.IsRichText() {
		//		currentContent, _ = m.glamourRenderer.Render(strings.Join(plainLines[:len(plainLines)-1], "\n"))
		//		m.paging.OverflowContent = plainLines[len(plainLines)-1]
		//	} else {
		//		currentContent = strings.Join(renderedLines[:len(renderedLines)-1], "\n")
		//		m.paging.OverflowContent = renderedLines[len(renderedLines)-1]
		//	}
		//
		//	// old:
		//	//m.paging.OverflowContent = renderedLines[len(renderedLines)-1]
		//	//currentContent = strings.Join(renderedLines[:len(renderedLines)-1], "\n")
		//	////panic("exit")
		//}
		//
		//m.lastValidContent = currentContent
		//m.Content = currentContent

	case FinishMsg:
		for strings.HasSuffix(m.Content, "\n") {
			m.Content = m.Content[:len(m.Content)-1]
		}
		m.Content += "\n"
		return m, tea.Quit

	case spinner.TickMsg:
		mgr, cmd := m.spinnerManager.Update(msg)
		m.spinnerManager = mgr.(*SpinnerManagerModel)
		if cmd != nil {
			cmds = append(cmds, cmd)
		}
	}

	return m, tea.Batch(cmds...)
}

// View renders the component
func (m FrameModel) View() string {
	termWidth := terminal.GetWidth()
	frameWidth := m.config.MaxWidth()
	if frameWidth > (termWidth - m.config.MarginX()*2 - 1) {
		frameWidth = termWidth - m.config.MarginX()*2 - 1
	}

	frameWidthOverhead := m.config.PaddingX() * 2
	contentMaxWidth := terminal.MaxLineWidth(m.Content)

	if contentMaxWidth < (frameWidth - frameWidthOverhead) {
		frameWidth = contentMaxWidth + frameWidthOverhead
	}
	if frameWidth < 25 {
		frameWidth = 25
	}

	// Create the base frame config with content alignment
	// TODO: move this to model (except call to Width())
	frameContainerStyle := lipgloss.NewStyle().
		Margin(m.config.MarginX(), m.config.MarginY()).
		BorderStyle(m.config.Border()).
		BorderForeground(m.config.BorderColor()).
		Padding(m.config.PaddingY(), m.config.PaddingX()).
		Align(m.config.AlignH()).
		Width(frameWidth)

	var content string

	if m.isConnecting || m.isThinking {
		content = m.spinnerManager.View()
		frameContainerStyle = frameContainerStyle.BorderForeground(m.config.BorderColorLight())
		frameContainerStyle = m.spinnerManager.AdjustContainerStyle(frameContainerStyle)
		if m.isConnecting {
			frameContainerStyle = frameContainerStyle.BorderStyle(m.config.WithIcon("🔌").Border())
		}
		if m.isThinking {
			frameContainerStyle = frameContainerStyle.BorderStyle(m.config.WithIcon("🤔").Border())
		}
	} else {
		content = m.Content
	}

	renderedContent := frameContainerStyle.Render(content)

	// For right-aligned frames, add padding to the left to push it to the right
	if m.config.AlignH() == lipgloss.Right {
		// Calculate padding needed (remaining 20% of terminal width)
		padding := strings.Repeat(" ", termWidth-frameWidth-2-m.config.MarginX()*2)

		// Add padding to each line
		lines := strings.Split(renderedContent, "\n")
		for i, line := range lines {
			lines[i] = padding + line
		}

		// Rejoin the lines
		renderedContent = strings.Join(lines, "\n")
	}

	return renderedContent
}

// StreamMessage creates a frame with vibrant pink border
// reading the content from an io.Reader in a streaming fashion
func StreamMessage(reader io.Reader, style FrameConfig) {
	//writer := paging.NewChunkedLineWriter(terminal.GetHeight() - 2)
	writer := paging.NewChunkedLineWriter(20)
	frame := NewFrame("", writer, style)
	streamDistributedFrames(frame, reader)
}

func ShowMessage(content string, style FrameConfig) {
	StreamMessage(strings.NewReader(content), style)
}

func StreamAia(reader io.Reader) {
	StreamMessage(reader, AiaStyle())
}

//func ShowAiaMessage(content string) {
//	ShowMessage(content, AiaStyle())
//}
//
//func StreamUserMessage(reader io.Reader) {
//	StreamMessage(reader, UserStyle())
//}
//
//func ShowUserMessage(content string) {
//	ShowMessage(content, UserStyle())
//}
//
//func StreamErrorMessage(reader io.Reader) {
//	StreamMessage(reader, ErrorStyle())
//}
//
//func ShowErrorMessage(content string) {
//	ShowMessage(content, ErrorStyle())
//}
//
//func StreamWarningMessage(reader io.Reader) {
//	StreamMessage(reader, WarningStyle())
//}
//
//func ShowWarningMessage(content string) {
//	ShowMessage(content, WarningStyle())
//}
//
//func StreamInfoMessage(reader io.Reader) {
//	StreamMessage(reader, InfoStyle())
//}
//
//func ShowInfoMessage(content string) {
//	ShowMessage(content, InfoStyle())
//}
//
//func StreamSuccessMessage(reader io.Reader) {
//	StreamMessage(reader, SuccessStyle())
//}
//
//func ShowSuccessMessage(content string) {
//	ShowMessage(content, SuccessStyle())
//}

func streamDistributedFrames(frame FrameModel, reader io.Reader) {
	p := tea.NewProgram(frame)

	bufReader := bufio.NewReader(reader)

	buf := make([]byte, 512)

	var start, read func()
	var wg sync.WaitGroup

	start = func() {
		wg.Add(1)
		go read()
		_, err := p.Run()
		if err != nil {
			panic(err)
		}
	}

	read = func() {
		defer wg.Done()
		for {
			n, err := bufReader.Read(buf)
			if n > 0 {
				msg := string(buf[:n])

				p.Send(contentMsg(msg))

				if len(frame.writer.Chunks()) < frame.pag.currentPageIdx {
					p.Send(FinishMsg{})
					frame.pag.currentPageIdx = len(frame.writer.Chunks())
					p = tea.NewProgram(frame)
					start()
					return
				}

				//if frame.paging.Overflown {
				//	overflowContent := frame.paging.OverflowContent
				//	p.Send(FinishMsg{})
				//	frame.paging = &frameModelPaging{}
				//	p = tea.NewProgram(frame)
				//	go p.Send(contentMsg(overflowContent + msg))
				//	start()
				//	return
				//}
			}

			if err != nil {
				if err == io.EOF {
					p.Send(FinishMsg{})
					p.Send(tea.Quit())
				}
				return
			}
		}
	}

	start()

	wg.Wait()
}
