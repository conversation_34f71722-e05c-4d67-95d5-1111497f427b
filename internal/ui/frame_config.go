package ui

import (
	"github.com/charmbracelet/lipgloss"
	"github.com/oxio/aia/internal/colors"
)

type FrameConfig struct {
	marginX          int
	marginY          int
	paddingX         int
	paddingY         int
	borderColor      lipgloss.Color
	borderColorLight lipgloss.Color
	border           lipgloss.Border
	alignH           lipgloss.Position
	maxWidth         int
	icon             string
	title            string
	isRichText       bool
}

// MarginX returns the horizontal margin
func (s FrameConfig) MarginX() int {
	return s.marginX
}

// WithMarginX sets the horizontal margin and returns a new config instance
func (s FrameConfig) WithMarginX(margin int) FrameConfig {
	new := s
	new.marginX = margin
	return new
}

// MarginY returns the vertical margin
func (s FrameConfig) MarginY() int {
	return s.marginY
}

// WithMarginY sets the vertical margin and returns a new config instance
func (s FrameConfig) WithMarginY(margin int) FrameConfig {
	new := s
	new.marginY = margin
	return new
}

// PaddingX returns the horizontal padding
func (s FrameConfig) PaddingX() int {
	if s.isRichText {
		return 0
	}
	return s.paddingX
}

func (s FrameConfig) PaddingXRaw() int {
	return s.paddingX
}

// WithPaddingX sets the horizontal padding and returns a new config instance
func (s FrameConfig) WithPaddingX(padding int) FrameConfig {
	new := s
	new.paddingX = padding
	return new
}

// PaddingY returns the vertical padding
func (s FrameConfig) PaddingY() int {
	if s.isRichText {
		return 0
	}
	return s.paddingY
}

func (s FrameConfig) PaddingYRaw() int {
	return s.paddingY
}

// WithPaddingY sets the vertical padding and returns a new config instance
func (s FrameConfig) WithPaddingY(padding int) FrameConfig {
	new := s
	new.paddingY = padding
	return new
}

// BorderColor returns the border color
func (s FrameConfig) BorderColor() lipgloss.Color {
	return s.borderColor
}

// WithBorderColor sets the border color and returns a new config instance
func (s FrameConfig) WithBorderColor(color lipgloss.Color) FrameConfig {
	new := s
	new.borderColor = color
	return new
}

func (s FrameConfig) BorderColorLight() lipgloss.Color {
	return s.borderColorLight
}

func (s FrameConfig) WithBorderColorLight(color lipgloss.Color) FrameConfig {
	new := s
	new.borderColorLight = color
	return new
}

// Border returns the border config with title and icon if present
func (s FrameConfig) Border() lipgloss.Border {
	// Create a copy of the border to avoid modifying the original
	border := lipgloss.Border{
		Top:         s.border.Top,
		Bottom:      s.border.Bottom,
		Left:        s.border.Left,
		Right:       s.border.Right,
		TopLeft:     s.border.TopLeft,
		TopRight:    s.border.TopRight,
		BottomLeft:  s.border.BottomLeft,
		BottomRight: s.border.BottomRight,
	}

	useTopLeft := border.TopLeft
	if s.icon != "" || s.title != "" {
		useTopLeft = border.TopLeft + border.Top
		if s.icon != "" {
			useTopLeft += " " + s.icon
		}
		if s.title != "" {
			useTopLeft += " " + s.title
		}
		useTopLeft += " "
	}

	border.TopLeft = useTopLeft
	return border
}

// WithBorder sets the border config and returns a new config instance
func (s FrameConfig) WithBorder(border lipgloss.Border) FrameConfig {
	new := s
	new.border = border
	return new
}

// AlignH returns the horizontal alignment
func (s FrameConfig) AlignH() lipgloss.Position {
	return s.alignH
}

// WithAlignH sets the horizontal alignment and returns a new config instance
func (s FrameConfig) WithAlignH(align lipgloss.Position) FrameConfig {
	new := s
	new.alignH = align
	return new
}

func (s FrameConfig) WithMaxWidth(width int) FrameConfig {
	new := s
	new.maxWidth = width
	return new
}

func (s FrameConfig) MaxWidth() int {
	if s.isRichText {
		return 80
	}
	return s.maxWidth
}

// Icon returns the icon
func (s FrameConfig) Icon() string {
	return s.icon
}

// WithIcon sets the icon and returns a new config instance
func (s FrameConfig) WithIcon(icon string) FrameConfig {
	new := s
	new.icon = icon
	return new
}

// Title returns the title
func (s FrameConfig) Title() string {
	return s.title
}

// WithTitle sets the title and returns a new config instance
func (s FrameConfig) WithTitle(title string) FrameConfig {
	new := s
	new.title = title
	return new
}

func (s FrameConfig) WithRichText() FrameConfig {
	if s.isRichText {
		return s
	}
	new := s
	new.isRichText = true
	return new
}

func (s FrameConfig) WithoutRichText() FrameConfig {
	if !s.isRichText {
		return s
	}
	new := s
	new.isRichText = false
	return new
}

func (s FrameConfig) IsRichText() bool {
	return s.isRichText
}

func NewFrameConfig() FrameConfig {
	return FrameConfig{
		marginX:          1,
		marginY:          1,
		paddingX:         2,
		paddingY:         1,
		borderColor:      colors.VibrantPink,
		borderColorLight: colors.DimmedPink5,
		border:           lipgloss.RoundedBorder(),
		alignH:           lipgloss.Left,
		maxWidth:         120,
		isRichText:       false,
	}
}

func AiaStyle() FrameConfig {
	return NewFrameConfig().
		WithBorderColor(colors.VibrantPink).
		//WithIcon("\u2728\u200B"). // sparkles
		//WithIcon("\U0001F31F").   // glowing star
		WithIcon("\U0001F4AB"). // dizzy
		WithTitle("AIA")
}

func UserStyle() FrameConfig {
	return NewFrameConfig().
		WithBorderColor(colors.DeepSkyBlue).
		WithIcon("👤").
		WithTitle("You").
		WithAlignH(lipgloss.Right)
}

func ErrorStyle() FrameConfig {
	return NewFrameConfig().
		WithBorderColor(colors.Red).
		WithIcon("❌").
		WithTitle("Error")
}

func WarningStyle() FrameConfig {
	return NewFrameConfig().
		WithBorderColor(colors.PaleGold).
		WithIcon("⚡").
		WithTitle("Warning")
}

func InfoStyle() FrameConfig {
	return NewFrameConfig().
		WithBorderColor(colors.SteelBlue).
		WithIcon("💡").
		WithTitle("Info")
}

func SuccessStyle() FrameConfig {
	return NewFrameConfig().
		WithBorderColor(colors.MintLeaf).
		WithIcon("✅").
		WithTitle("Success")
}

// WithMargin sets both horizontal and vertical margins and returns a new config instance
func (s FrameConfig) WithMargin(vertical, horizontal int) FrameConfig {
	new := s
	new.marginX = horizontal
	new.marginY = vertical
	return new
}

// WithPadding sets both horizontal and vertical paddings and returns a new config instance
func (s FrameConfig) WithPadding(vertical, horizontal int) FrameConfig {
	new := s
	new.paddingX = horizontal
	new.paddingY = vertical
	return new
}
