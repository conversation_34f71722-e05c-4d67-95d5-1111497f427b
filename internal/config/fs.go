package config

import (
	"os"
	"path/filepath"
)

const configFile = "aia_config.yml"

var (
	configDirPath      string
	mainConfigFilePath string
	logsDirPath        string
	mainLogFilePath    string
)

func GetConfigDirPath() string {
	return configDirPath
}

func GetMainConfigFilePath() string {
	return mainConfigFilePath
}

func GetLogDirPath() string {
	return logsDirPath
}

func GetMainLogFilePath() string {
	return mainLogFilePath
}

func init() {
	userConfigDir, err := os.UserConfigDir()
	if err != nil {
		panic(err)
	}

	configDirPath = filepath.Join(userConfigDir, "aia")
	mainConfigFilePath = filepath.Join(configDirPath, configFile)
	logsDirPath = filepath.Join(configDirPath, "logs")
	mainLogFilePath = filepath.Join(logsDirPath, "aia.log")
}
