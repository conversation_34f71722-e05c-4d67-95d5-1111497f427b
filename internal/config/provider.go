package config

import (
	"errors"
	"github.com/elliotchance/orderedmap/v3"
)

type providerCode string

const (
	OpenAiApiProviderCode     providerCode = "openai-api"
	OllamaApiProviderCode     providerCode = "ollama-api"
	GroqApiProviderCode       providerCode = "groq-api"
	OpenRouterApiProviderCode providerCode = "openrouter-api"
	CustomProviderCode        providerCode = "customprovider-api"
)

type Provider struct {
	Code             providerCode `yaml:"code"`
	Name             string       `yaml:"name"`
	Description      string       `yaml:"description"`
	DefaultBaseUrl   string       `yaml:"default_base_url"`
	DefaultMainModel string       `yaml:"default_main_model"`
	DefaultMetaModel string       `yaml:"default_meta_model"`
}

type Providers struct {
	*orderedmap.OrderedMap[providerCode, Provider]
}

var PredefinedProviders *Providers

var (
	ProviderNotFoundError = errors.New("provider not found")
)

func init() {
	PredefinedProviders = &Providers{
		orderedmap.NewOrderedMap[providerCode, Provider](),
	}
	PredefinedProviders.Set(
		OpenAiApiProviderCode,
		Provider{
			Code:             OpenAiApiProviderCode,
			Name:             "OpenAI",
			Description:      "Home of the ChatGPT",
			DefaultBaseUrl:   "https://api.openai.com/v1/",
			DefaultMainModel: "gpt-4o-mini",
			DefaultMetaModel: "gpt-4o-mini",
		},
	)
	PredefinedProviders.Set(
		OllamaApiProviderCode,
		Provider{
			Code:             OllamaApiProviderCode,
			Name:             "Ollama",
			Description:      "Connect to Ollama API, which you can run on your local machine",
			DefaultBaseUrl:   "http://localhost:11434/v1/",
			DefaultMainModel: "aya-expanse:8b",
			DefaultMetaModel: "llama3.2:1b",
		},
	)
	PredefinedProviders.Set(
		GroqApiProviderCode,
		Provider{
			Code:             GroqApiProviderCode,
			Name:             "Groq",
			Description:      "Has models known for its speed and low latency",
			DefaultBaseUrl:   "https://api.groq.com/openai/v1/",
			DefaultMainModel: "deepseek-r1-distill-llama-70b",
			DefaultMetaModel: "llama-3.1-8b-instant",
		},
	)
	PredefinedProviders.Set(
		OpenRouterApiProviderCode,
		Provider{
			Code:             OpenRouterApiProviderCode,
			Name:             "OpenRouter",
			Description:      "OAggregates multiple popular models",
			DefaultBaseUrl:   "https://openrouter.ai/api/v1/",
			DefaultMainModel: "google/gemini-2.0-flash-exp:free",
			DefaultMetaModel: "google/gemini-2.0-flash-lite-preview-02-05:free",
		},
	)
	PredefinedProviders.Set(
		CustomProviderCode,
		Provider{
			Code:             CustomProviderCode,
			Name:             "Custom...",
			Description:      "Configure custom provider compatible with OpenAI API",
			DefaultBaseUrl:   "",
			DefaultMainModel: "",
			DefaultMetaModel: "",
		},
	)
}

func (d *Providers) GetIndexByCode(code providerCode) int {
	idx := 0
	for key := range d.Keys() {
		if key == code {
			return idx
		}
		idx++
	}
	return -1
}

func (d *Providers) GetCodeByName(name string) (*Provider, error) {
	for _, provider := range d.AllFromBack() {
		if provider.Name == name {
			return &provider, nil
		}
	}

	return nil, ProviderNotFoundError
}
