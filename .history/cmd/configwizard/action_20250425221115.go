package configwizard

import (
	"context"
	"slices"
	"strings"

	tea "github.com/charmbracelet/bubbletea"
	"github.com/oxio/aia/cmd/cmdcommon"
	"github.com/oxio/aia/internal/config"
)

const ActionName = "config"

func IsAction(args []string) bool {
	if len(args) != 1 {
		return false
	}
	firstArg := strings.ToLower(strings.TrimSpace(args[0]))
	if !slices.Contains([]string{"config", "setup", "configure"}, firstArg) {
		return false
	}
	return true
}

func Run(ctx context.Context, cfg *config.Config) error {
	var mainModel *config.Model

	if len(cfg.Models) == 0 {
		mainModel = &config.Model{
			Name: "main",
			Role: config.ModelMainRole,
		}
		cfg.Models = append(cfg.Models, mainModel)
	} else {
		for _, model := range cfg.Models {
			if mainModel == nil {
				// ensure that main model is always defined: by default use the first one
				mainModel = model
			}
			if model.Role == config.ModelMainRole {
				mainModel = model
				break
			}
		}
	}

	err := setupModel(ctx, mainModel)
	if err != nil {
		return err
	}

	return config.Save(cfg)
}

func setupModel(ctx context.Context, cfgModel *config.Model) error {
	// TODO: 1) initially select the provider based on current config
	// TODO: 2) initially select the model based on current config
	cpTitle := "(1/3) Setup new model"
	if cfgModel.Name != "" {
		cpTitle = "(1/3) Setup model: " + cfgModel.Name
	}

	chooseProvider := newChooseProviderView(cpTitle, config.PredefinedProviders)
	creds := newCredsView(ctx, "(2/3) Setup API access", cfgModel.APIBaseURL, cfgModel.APIKey)
	root := newRootView(2, chooseProvider)

	chooseProvider.root = root
	chooseProvider.next = creds

	creds.root = root
	creds.prev = chooseProvider

	p := tea.NewProgram(root, tea.WithAltScreen())
	creds.program = p

	if _, err := p.Run(); err != nil {
		return err
	}

	if root.quitting {
		return cmdcommon.UserQuitError
	}

	cfgModel.Provider = chooseProvider.chosenProvider.Code
	cfgModel.APIBaseURL = creds.apiBaseURL
	cfgModel.APIKey = creds.apiKey

	return nil
}
